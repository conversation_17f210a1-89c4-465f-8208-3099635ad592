package com.heal.controlcenter.dao.mysql.entity;

import com.heal.configuration.pojos.BasicEntity;
import com.heal.controlcenter.beans.AgentBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * DAO for agent operations.
 */
@Slf4j
@Repository
public class AgentDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets agent bean data by identifier.
     * Original JDBI query: "select a.id,unique_token uniqueToken,a.name,a.agent_type_id agentTypeId,a.created_time createdTime,a.updated_time updatedTime,a.user_details_id userDetailsId,a.status, a.host_address hostAddress, a.mode, a.description, a.physical_agent_id physicalAgentId, a.forensics_enabled forensicsEnabled, a.version, pa.identifier physicalAgentIdentifier from agent a, physical_agent pa where unique_token = :agentUid and a.physical_agent_id=pa.id"
     * @param agentUid The agent unique token
     * @return AgentBean if found, null otherwise
     */
    public AgentBean getAgentBeanData(String agentUid) {
        String sql = "SELECT a.id, unique_token as uniqueToken, a.name, a.agent_type_id as agentTypeId, " +
                "a.created_time as createdTime, a.updated_time as updatedTime, a.user_details_id as userDetailsId, " +
                "a.status, a.host_address as hostAddress, a.mode, a.description, a.physical_agent_id as physicalAgentId, " +
                "a.forensics_enabled as forensicsEnabled, a.version, pa.identifier as physicalAgentIdentifier " +
                "FROM agent a " +
                "INNER JOIN physical_agent pa ON a.physical_agent_id = pa.id " +
                "WHERE a.unique_token = ?";

        try {
            List<AgentBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                AgentBean bean = new AgentBean();
                bean.setId(rs.getInt("id"));
                bean.setUniqueToken(rs.getString("uniqueToken"));
                bean.setName(rs.getString("name"));
                bean.setAgentTypeId(rs.getInt("agentTypeId"));
                bean.setCreatedTime(rs.getString("createdTime"));
                bean.setUpdatedTime(rs.getString("updatedTime"));
                bean.setUserDetailsId(rs.getString("userDetailsId"));
                bean.setLastModifiedBy(rs.getString("userDetailsId")); // Map to lastModifiedBy as well
                bean.setStatus(rs.getInt("status"));
                bean.setHostAddress(rs.getString("hostAddress"));
                bean.setMode(rs.getString("mode"));
                bean.setDescription(rs.getString("description"));
                bean.setPhysicalAgentId(rs.getInt("physicalAgentId"));
                bean.setForensicsEnabled(rs.getBoolean("forensicsEnabled"));
                bean.setVersion(rs.getString("version"));
                bean.setPhysicalAgentIdentifier(rs.getString("physicalAgentIdentifier"));
                return bean;
            }, agentUid);

            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error getting agent bean data: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Gets agent to instance mapping using JDBC with proper joins.
     * @param instanceId The component instance ID
     * @param accountId The account ID
     * @param agentId The agent ID
     * @return List of BasicEntity objects representing the mapping
     */
    public List<BasicEntity> getAgentToInstanceMapping(int instanceId, int accountId, int agentId) {
        String sql = "SELECT c.id, c.name, c.identifier, " +
                "c.created_time AS createdTime, c.updated_time AS updatedTime, " +
                "c.user_details_id AS lastModifiedBy, a.status " +
                "FROM agent_comp_instance_mapping acim " +
                "INNER JOIN comp_instance c ON c.id = acim.comp_instance_id " +
                "INNER JOIN agent a ON a.id = acim.agent_id " +
                "WHERE acim.agent_id = ? AND c.account_id = ? AND c.id = ?";

        try {
            return jdbcTemplate.query(sql, (rs, rowNum) -> {
                BasicEntity entity = new BasicEntity();
                entity.setId(rs.getInt("id"));
                entity.setName(rs.getString("name"));
                entity.setIdentifier(rs.getString("identifier"));
                entity.setCreatedTime(rs.getString("createdTime"));
                entity.setUpdatedTime(rs.getString("updatedTime"));
                entity.setLastModifiedBy(rs.getString("lastModifiedBy"));
                entity.setStatus(rs.getInt("status"));
                return entity;
            }, agentId, accountId, instanceId);
        } catch (Exception e) {
            log.error("Error occurred while fetching agent to instance mapping for agentId: {}, accountId: {}, instanceId: {}. Details: ",
                    agentId, accountId, instanceId, e);
            return Collections.emptyList();
        }
    }
}
