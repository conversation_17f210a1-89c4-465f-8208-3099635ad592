package com.heal.controlcenter.dao.mysql.entity;

import com.heal.configuration.pojos.BasicEntity;
import com.heal.controlcenter.beans.AgentBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DAO for agent operations.
 */
@Slf4j
@Repository
public class AgentDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets agent bean data by identifier.
     * Original JDBI query: "select a.id,unique_token uniqueToken,a.name,a.agent_type_id agentTypeId,a.created_time createdTime,a.updated_time updatedTime,a.user_details_id userDetailsId,a.status, a.host_address hostAddress, a.mode, a.description, a.physical_agent_id physicalAgentId, a.forensics_enabled forensicsEnabled, a.version, pa.identifier physicalAgentIdentifier from agent a, physical_agent pa where unique_token = :agentUid and a.physical_agent_id=pa.id"
     * @param agentUid The agent unique token
     * @return AgentBean if found, null otherwise
     */
    public AgentBean getAgentBeanData(String agentUid) {
        String sql = "SELECT a.id, unique_token as uniqueToken, a.name, a.agent_type_id as agentTypeId, " +
                "a.created_time as createdTime, a.updated_time as updatedTime, a.user_details_id as userDetailsId, " +
                "a.status, a.host_address as hostAddress, a.mode, a.description, a.physical_agent_id as physicalAgentId, " +
                "a.forensics_enabled as forensicsEnabled, a.version, pa.identifier as physicalAgentIdentifier " +
                "FROM agent a, physical_agent pa WHERE unique_token = ? AND a.physical_agent_id = pa.id";

        try {
            List<AgentBean> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                AgentBean bean = new AgentBean();
                bean.setId(rs.getInt("id"));
                bean.setUniqueToken(rs.getString("uniqueToken"));
                bean.setName(rs.getString("name"));
                bean.setAgentTypeId(rs.getInt("agentTypeId"));
                bean.setCreatedTime(rs.getString("createdTime"));
                bean.setUpdatedTime(rs.getString("updatedTime"));
                bean.setUserDetailsId(rs.getString("userDetailsId"));
                bean.setLastModifiedBy(rs.getString("userDetailsId")); // Map to lastModifiedBy as well
                bean.setStatus(rs.getInt("status"));
                bean.setHostAddress(rs.getString("hostAddress"));
                bean.setMode(rs.getString("mode"));
                bean.setDescription(rs.getString("description"));
                bean.setPhysicalAgentId(rs.getInt("physicalAgentId"));
                bean.setForensicsEnabled(rs.getBoolean("forensicsEnabled"));
                bean.setVersion(rs.getString("version"));
                bean.setPhysicalAgentIdentifier(rs.getString("physicalAgentIdentifier"));
                return bean;
            }, agentUid);

            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("Error getting agent bean data: {}", e.getMessage(), e);
            return null;
        }
    }

    @RegisterMapperFactory(BeanMapperFactory.class)
    @SqlQuery("select c.id, c.name, c.identifier, c.created_time createdTime, c.updated_time updatedTime, c.user_details_id lastModifiedBy, a.status from agent_comp_instance_mapping, comp_instance c, agent a where a.id=agent_id and c.id=comp_instance_id and agent_id = :agentId and c.account_id=:accountId and c.id=:instanceId")
    List<BasicEntity> getAgentToInstanceMapping(@Bind("instanceId") int instanceId, @Bind("accountId") int accountId, @Bind("agentId") int agentId);
}
